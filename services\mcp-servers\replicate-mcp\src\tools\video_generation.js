const { z } = require('zod');
const Replicate = require('replicate');

/**
 * Create a video generation tool for the Replicate MCP server
 * @param {Object} server - The MCP server instance
 */
function registerVideoGenerationTool(server) {
  server.tool(
    "generate-video",
    "Generate a video from a prompt or image using state-of-the-art video generation models",
    {
      model: z.string().describe("Model identifier (e.g., 'meta/veo-2', 'stability-ai/stable-video-diffusion')"),
      prompt: z.string().describe("Text prompt for video generation"),
      image_url: z.string().optional().describe("Optional input image URL for image-to-video generation"),
      seconds: z.number().optional().default(3).describe("Video length in seconds"),
      guidance_scale: z.number().optional().describe("Control how closely the video matches the prompt"),
      fps: z.number().optional().default(24).describe("Frames per second"),
      motion_bucket_id: z.number().optional().describe("Controls the amount of motion (higher = more motion)")
    },
    async ({ model, prompt, image_url, seconds = 3, guidance_scale, fps = 24, motion_bucket_id, ...otherOptions }) => {
      try {
        // Initialize Replicate client (expecting API key in env)
        const replicate = new Replicate({
          auth: process.env.REPLICATE_API_KEY,
        });

        // Construct input based on model and parameters
        const input = {
          prompt,
          ...(image_url && { image: image_url }),
          ...(seconds && { seconds }),
          ...(guidance_scale && { guidance_scale }),
          ...(fps && { fps }),
          ...(motion_bucket_id && { motion_bucket_id }),
          ...otherOptions // Pass through any additional model-specific options
        };

        console.log(`Generating video with model: ${model}`);
        console.log(`Prompt: "${prompt}"`);
        if (image_url) console.log(`Input image: ${image_url}`);

        // Run the model
        const output = await replicate.run(model, { input });

        console.log("Received output from Replicate:", output);

        // Different models return different output formats
        // Veo returns { video: "url" }
        // SVD might return { video_frames: [...], output_video: "url" }
        const videoUrl = output.video || output.output_video || null;

        if (!videoUrl) {
          throw new Error(`Model ${model} did not return a valid video URL. Raw output: ${JSON.stringify(output)}`);
        }

        return {
          content: [
            {
              type: "text",
              text: JSON.stringify({
                video_url: videoUrl,
                model: model,
                prompt: prompt,
                duration: seconds,
                additional_info: output
              })
            }
          ]
        };
      } catch (error) {
        console.error("Error generating video:", error);
        return {
          content: [
            {
              type: "text",
              text: JSON.stringify({
                error: "Failed to generate video",
                message: error.message,
                details: error.stack
              })
            }
          ]
        };
      }
    }
  );

  console.log("Video generation tool registered");
}

module.exports = { registerVideoGenerationTool };
