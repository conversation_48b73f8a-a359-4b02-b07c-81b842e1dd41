"""
Prayer Routes for SpiritSync API
Handles prayer generation endpoints.
"""

from fastapi import APIRouter, BackgroundTasks, HTTPException, WebSocket, WebSocketDisconnect
from pydantic import BaseModel, Field
from typing import List, Dict, Optional, Any
import os
import uuid
import json
import asyncio
from datetime import datetime

# Import from the parent directory
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Try to import the prayer workflow
try:
    from graph_definition_new import create_prayer_workflow
    from models.prayer_state import PrayerState
    from utils.file_utils import create_run_directory
except ImportError as e:
    print(f"[WARNING] Error importing backend modules in routes: {e}")

# Import WebSocket connection manager
from websockets.connection_manager import manager

router = APIRouter(tags=["Prayer"])

# Storage for active prayer runs
active_runs = {}

# Pydantic model for prayer generation input
class PrayerInputAPI(BaseModel):
    pray_for: str = Field(..., min_length=1)
    wisdom_to_integrate: str = Field(..., min_length=1)
    selected_faiths: List[str] = Field(default=[])
    generate_images: bool = Field(default=False)
    voice_id: Optional[str] = None

@router.post("/prayer/generate")
async def generate_prayer(input: PrayerInputAPI, background_tasks: BackgroundTasks):
    """Start a new prayer generation process"""
    
    # Create a unique run ID
    run_id = str(uuid.uuid4())
    
    # Create run-specific output directory
    try:
        run_output_dir = create_run_directory()
        print(f"[INFO] Created run directory: {run_output_dir}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create run directory: {str(e)}")
    
    # Initialize the workflow state
    prayer_focus_theme = f"{input.pray_for}, integrating wisdom about {input.wisdom_to_integrate}"
    
    # Create initial state
    initial_state = {
        # Core prayer information
        "prayer_focus_theme": prayer_focus_theme,
        "search_context": "",
        "religious_terms": {},
        "faith_research": {},

        # Workflow state tracking
        "spiritual_movements_to_process": input.selected_faiths.copy(),
        "individual_prayers": {},
        "current_movement": None,
        "run_output_dir": run_output_dir,
        "error_message": None,
        "current_status": "starting",
        "progress_percentage": 0,
        "status_details": "Initializing prayer generation",

        # Unified prayer fields
        "combined_prayer_text": None,
        "unified_prayer_markdown": None,
        "unified_prayer_filename": None,
        "unified_prayer_filepath": None,

        # TTS fields
        "audio_conversion_requested": True,
        "tts_confirmed": True,
        "tts_provider": None,
        "tts_voice_id": input.voice_id,
        "tts_options": None,
        "verbal_prayer_text": None,
        "verbal_prayer_filepath": None,
        "audio_filepath": None,

        # Image prayer fields
        "image_prayer_requested": input.generate_images,
        "image_prayer_generated": False,
        "image_prayer_filepaths": None
    }
    
    # Store in active runs
    active_runs[run_id] = {
        "state": initial_state,
        "start_time": datetime.now().isoformat(),
        "callbacks": [],  # For WebSocket callbacks
        "completed": False,
        "status": "starting",
        "progress": 0
    }
    
    # Start the workflow in a background task
    background_tasks.add_task(run_prayer_workflow, run_id, initial_state)
    
    return {"run_id": run_id, "status": "started"}

@router.get("/prayer/status/{run_id}")
async def get_prayer_status(run_id: str):
    """Get the status of a prayer generation process"""
    if run_id not in active_runs:
        raise HTTPException(status_code=404, detail="Prayer run not found")
    
    return active_runs[run_id]

@router.websocket("/prayer/stream/{run_id}")
async def stream_prayer_status(websocket: WebSocket, run_id: str):
    """Stream updates for a prayer generation process"""
    await manager.connect(websocket, run_id)
    
    if run_id not in active_runs:
        await websocket.send_json({"error": "Prayer run not found"})
        manager.disconnect(websocket, run_id)
        return
    
    try:
        # Send initial state
        await websocket.send_json(active_runs[run_id])
        
        # Keep connection alive until client disconnects
        while True:
            try:
                # Wait for any message from client (ping or close)
                data = await websocket.receive_text()
                if data == "ping":
                    await websocket.send_json({"type": "pong"})
            except WebSocketDisconnect:
                break
            
    except Exception as e:
        print(f"WebSocket error: {e}")
    finally:
        # Disconnect the WebSocket client
        manager.disconnect(websocket, run_id)

async def run_prayer_workflow(run_id: str, initial_state: Any):
    """Run the prayer generation workflow"""
    try:
        # Create workflow
        workflow = create_prayer_workflow()
        
        # Execute workflow
        for event in workflow.stream(initial_state, {"recursion_limit": 60}):
            if "__end__" not in event:
                # Get the latest state key (node name)
                latest_state_key = list(event.keys())[0]
                
                # Update the current status
                active_runs[run_id]["status"] = latest_state_key
                
                # Get the latest state
                latest_state = event[latest_state_key]
                
                # Convert to dict if it's an object
                if not isinstance(latest_state, dict):
                    if hasattr(latest_state, "dict"):
                        latest_state = latest_state.dict()
                    else:
                        latest_state = vars(latest_state)
                
                # Update progress based on current stage
                if latest_state_key == "perform_research":
                    active_runs[run_id]["progress"] = 10
                elif latest_state_key == "prepare_movement":
                    active_runs[run_id]["progress"] = 20
                elif latest_state_key == "generate_prayer":
                    active_runs[run_id]["progress"] = 30
                elif latest_state_key == "save_prayer":
                    active_runs[run_id]["progress"] = 40
                elif latest_state_key == "aggregate_prayers":
                    active_runs[run_id]["progress"] = 50
                elif latest_state_key == "generate_unified":
                    active_runs[run_id]["progress"] = 60
                elif latest_state_key == "save_unified":
                    active_runs[run_id]["progress"] = 70
                elif latest_state_key == "prepare_tts_text":
                    active_runs[run_id]["progress"] = 80
                elif latest_state_key == "generate_audio":
                    active_runs[run_id]["progress"] = 90
                elif latest_state_key == "generate_image_prayer":
                    active_runs[run_id]["progress"] = 95
                
                # Store the updated state
                active_runs[run_id]["state"] = latest_state
                
                # Notify WebSocket clients
                asyncio.create_task(manager.broadcast(run_id, active_runs[run_id]))
                
        # Set as completed
        active_runs[run_id]["completed"] = True
        active_runs[run_id]["progress"] = 100
        active_runs[run_id]["status"] = "completed"
        
        # Notify WebSocket clients
        asyncio.create_task(manager.broadcast(run_id, active_runs[run_id]))
            
    except Exception as e:
        # Handle errors
        import traceback
        traceback.print_exc()
        
        active_runs[run_id]["error"] = str(e)
        active_runs[run_id]["status"] = "error"
        
        # Notify WebSocket clients
        asyncio.create_task(manager.broadcast(run_id, active_runs[run_id]))
