"""
Async Video Generation Nodes for LangGraph
Handles parallel video processing with progress tracking
"""

import asyncio
import logging
import os
from typing import Dict, List, Any
from models.prayer_state import PrayerState
import replicate
from replicate.exceptions import ModelError
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

async def async_video_generation_node(state: PrayerState) -> PrayerState:
    """
    Generate videos asynchronously for prayer scenes using Replicate Python SDK.
    Handles parallel video processing with progress tracking.
    """
    logger.info("Starting async video generation with Replicate SDK")
    
    try:
        generate_video = state.get('generate_video', False)
        if not generate_video:
            logger.info("Video generation not requested, skipping")
            state.setdefault('execution_logs', []).append("Video generation skipped - not requested")
            return state
        
        scenes = state.get('sacred_scenes', [])
        if not scenes:
            logger.warning("No scenes available for video generation")
            state.setdefault('execution_logs', []).append("Video generation skipped - no scenes available")
            return state
        
        replicate_api_token = os.getenv("REPLICATE_API_TOKEN")
        if not replicate_api_token:
            logger.error("REPLICATE_API_TOKEN not found in environment variables.")
            state.setdefault('execution_logs', []).append("Video generation failed - REPLICATE_API_TOKEN not set")
            state['video_generation_error'] = "REPLICATE_API_TOKEN not found"
            return state
        else:
            logger.info(f"REPLICATE_API_TOKEN found: {replicate_api_token[:5]}...") # Log masked token

        # Initialize Replicate client with explicit API token
        client = replicate.Client(api_token=replicate_api_token)
        
        state.setdefault('execution_logs', []).append(f"Starting video generation for {len(scenes)} scenes using Replicate")
        
        # Prepare tasks for parallel video generation
        video_tasks = []
        for i, scene in enumerate(scenes):
            prompt = scene.get("visual_description", scene.get("content", "")) # Use visual_description or content
            if prompt:
                logger.info(f"Scene {i} prompt for video generation: {prompt[:100]}...")
                
                # Create a coroutine for video generation - Fixed async handling
                async def generate_single_video(prompt_text, scene_index):
                    try:
                        # Use run_in_executor to handle potentially blocking replicate calls
                        loop = asyncio.get_event_loop()
                        result = await loop.run_in_executor(
                            None, 
                            lambda: client.run(
                                "pixverse/pixverse-v4.5",
                                input={"prompt": prompt_text, "fps": 24, "seconds": 3}
                            )
                        )
                        return {"scene_index": scene_index, "result": result}
                    except Exception as e:
                        return {"scene_index": scene_index, "error": str(e)}
                
                video_tasks.append(generate_single_video(prompt, i))
            else:
                logger.warning(f"Scene {i} has no description, skipping video generation for this scene.")
                state.setdefault('execution_logs', []).append(f"Scene {i} skipped - no description for video generation")

        if not video_tasks:
            logger.warning("No valid scenes with descriptions for video generation.")
            state.setdefault('execution_logs', []).append("No valid scenes for video generation.")
            return state

        # Run all video generation tasks in parallel
        raw_video_results = await asyncio.gather(*video_tasks, return_exceptions=True)
        
        successful_videos = []
        failed_videos = []
        
        for result in raw_video_results:
            if isinstance(result, Exception):
                failed_videos.append({"scene_index": "unknown", "error": str(result)})
                logger.error(f"Video generation task failed: {result}")
            elif isinstance(result, dict):
                scene_index = result.get("scene_index", 0)
                if "error" in result:
                    failed_videos.append({"scene_index": scene_index, "error": result["error"]})
                    logger.error(f"Scene {scene_index} video generation failed: {result['error']}")
                else:
                    video_url = result.get("result")
                    if video_url:
                        successful_videos.append({"scene_index": scene_index, "video_url": video_url})
                        logger.info(f"Scene {scene_index} video generated successfully: {video_url}")
                    else:
                        failed_videos.append({"scene_index": scene_index, "error": "No video URL returned"})
            else:
                failed_videos.append({"scene_index": "unknown", "error": f"Unexpected result type: {type(result)}"})
        
        state['generated_videos'] = successful_videos
        state['video_generation_errors'] = failed_videos
        
        # Update scenes with video URLs
        for video_result in successful_videos:
            scene_index = video_result.get("scene_index", 0)
            if scene_index < len(state['sacred_scenes']):
                state['sacred_scenes'][scene_index]["video_url"] = video_result.get("video_url")
        
        if successful_videos:
            state.setdefault('execution_logs', []).append(f"Successfully generated {len(successful_videos)} videos")
            state['generated_video'] = successful_videos[0].get("video_url") if successful_videos else None
        
        if failed_videos:
            state.setdefault('execution_logs', []).append(f"Failed to generate {len(failed_videos)} videos")
        
        logger.info(f"Video generation completed: {len(successful_videos)} successful, {len(failed_videos)} failed")
        
    except Exception as e:
        logger.error(f"Error in async video generation: {e}")
        state.setdefault('execution_logs', []).append(f"Video generation error: {str(e)}")
        state['video_generation_error'] = str(e)
    
    return state


async def parallel_media_generation_node(state: PrayerState) -> PrayerState:
    """
    Run image, video, and audio generation in parallel
    This is the main coordination node for async media processing
    """
    logger.info("Starting parallel media generation")
    
    try:
        # Prepare tasks for parallel execution
        tasks = []
        task_names = []
        
        # Video generation task
        if state.get('generate_video', False):
            tasks.append(async_video_generation_node(state.copy()))  # Pass copy to avoid state conflicts
            task_names.append("video_generation")
        
        # Image generation task
        if state.get('image_prayer_requested', False):
            tasks.append(async_image_generation_task(state.copy()))  # Pass copy to avoid state conflicts
            task_names.append("image_generation")
        
        # Audio generation task
        if state.get('audio_conversion_requested', False):
            tasks.append(async_audio_generation_task(state.copy()))  # Pass copy to avoid state conflicts
            task_names.append("audio_generation")
        
        if not tasks:
            logger.info("No media generation tasks requested")
            state.setdefault('execution_logs', []).append("No media generation tasks to run")
            return state
        
        state.setdefault('execution_logs', []).append(f"Running {len(tasks)} media generation tasks in parallel: {', '.join(task_names)}")
        
        # Run all media generation tasks in parallel
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results and merge state updates
        for i, result in enumerate(results):
            task_name = task_names[i]
            
            if isinstance(result, Exception):
                logger.error(f"Task {task_name} failed: {result}")
                state.setdefault('execution_logs', []).append(f"Media generation task '{task_name}' failed: {str(result)}")
            else:
                # Merge successful results back into main state
                result_videos = result.get('generated_videos')
                if result_videos and isinstance(result_videos, list):
                    state['generated_videos'] = state.get('generated_videos', []) + result_videos
                
                result_images = result.get('generated_images')
                if result_images and isinstance(result_images, list):
                    state['generated_images'] = state.get('generated_images', []) + result_images
                
                if result.get('generated_audio'):
                    state['generated_audio'] = result['generated_audio']
                
                # Merge execution logs
                if result.get('execution_logs'):
                    state.setdefault('execution_logs', []).extend(result['execution_logs'])
                
                logger.info(f"Task {task_name} completed successfully")
        
        state.setdefault('execution_logs', []).append("Parallel media generation completed")
        
    except Exception as e:
        logger.error(f"Error in parallel media generation: {e}")
        state.setdefault('execution_logs', []).append(f"Parallel media generation error: {str(e)}")
    
    return state


async def async_image_generation_task(state: PrayerState) -> PrayerState:
    """
    Async wrapper for image generation
    This allows image generation to run in parallel with video generation
    """
    logger.info("Starting async image generation")
    
    try:
        # Import the existing image generation node
        from nodes.updated_image_prayer_node import generate_image_prayer_node
        
        # Run the image generation
        result_state = await generate_image_prayer_node(state) # Await directly
        
        logger.info("Async image generation completed")
        return result_state
        
    except Exception as e:
        logger.error(f"Error in async image generation: {e}")
        state.setdefault('execution_logs', []).append(f"Async image generation error: {str(e)}")
        return state


async def async_audio_generation_task(state: PrayerState) -> PrayerState:
    """
    Async wrapper for audio generation
    This allows audio generation to run in parallel with other media
    """
    logger.info("Starting async audio generation")
    
    try:
        # Import the existing audio generation nodes
        from nodes.tts_nodes import prepare_tts_text_node, generate_audio_node
        
        # Check that we have the required state fields
        if not state.get('unified_prayer_filepath'):
            error_msg = "No unified_prayer_filepath found in state for audio generation"
            logger.error(error_msg)
            state.setdefault('execution_logs', []).append(error_msg)
            return state
        
        # Run TTS preparation and generation in thread executor
        # to avoid blocking the event loop
        loop = asyncio.get_event_loop()
        
        # Run TTS preparation in a separate thread
        state = await loop.run_in_executor(None, prepare_tts_text_node, state)
        
        # Run audio generation in a separate thread
        state = await loop.run_in_executor(None, generate_audio_node, state)
        
        logger.info("Async audio generation completed")
        return state
        
    except Exception as e:
        logger.error(f"Error in async audio generation: {e}")
        # Handle state consistently
        if isinstance(state, dict):
            state.setdefault('execution_logs', []).append(f"Async audio generation error: {str(e)}")
        else:
            execution_logs = getattr(state, 'execution_logs', [])
            execution_logs.append(f"Async audio generation error: {str(e)}")
            state.execution_logs = execution_logs
        return state


def should_run_parallel_media_generation(state: PrayerState) -> str:
    """
    Conditional check: Should we run parallel media generation?
    """
    generate_video = state.get('generate_video', False)
    generate_images = state.get('image_prayer_requested', False) # Use image_prayer_requested
    audio_requested = state.get('audio_conversion_requested', False)
    
    # Run parallel generation if any media type is requested
    if generate_video or generate_images or audio_requested:
        logger.info("Parallel media generation requested")
        return "run_parallel_media"
    else:
        logger.info("No media generation requested")
        return "skip_media"


async def media_aggregation_node(state: PrayerState) -> PrayerState:
    """
    Aggregate all generated media and create final unified output
    """
    logger.info("Starting media aggregation")
    
    try:
        # Create a comprehensive media summary
        media_summary = {}
        
        # Video summary
        if state.get('generated_videos'):
            media_summary['videos'] = {
                'count': len(state['generated_videos']),
                'urls': [v.get('video_url') for v in state['generated_videos'] if v.get('video_url')],
                'primary_video': state['generated_videos'][0].get('video_url') if state['generated_videos'] else None
            }
            state['generated_video'] = media_summary['videos']['primary_video']
        
        # Image summary
        if state.get('generated_images'):
            media_summary['images'] = {
                'count': len(state['generated_images']),
                'urls': state['generated_images']
            }
        
        # Audio summary
        if state.get('generated_audio'):
            media_summary['audio'] = {
                'url': state['generated_audio']
            }
        
        # Update unified prayer with media links
        if state.get('unified_prayer_markdown'):
            # Add media section to the unified prayer
            media_section = "\n\n## Generated Media\n\n"
            
            if 'videos' in media_summary:
                media_section += f"### Prayer Videos ({media_summary['videos']['count']} generated)\n"
                for i, url in enumerate(media_summary['videos']['urls']):
                    media_section += f"- [Prayer Video {i+1}]({url})\n"
                media_section += "\n"
            
            if 'images' in media_summary:
                media_section += f"### Prayer Images ({media_summary['images']['count']} generated)\n"
                for i, url in enumerate(media_summary['images']['urls']):
                    media_section += f"- [Prayer Image {i+1}]({url})\n"
                media_section += "\n"
            
            if 'audio' in media_summary:
                media_section += f"### Prayer Audio\n"
                media_section += f"- [Prayer Audio]({media_summary['audio']['url']})\n\n"
            
            # Append media section to unified prayer
            state['unified_prayer_markdown'] += media_section
        
        state['media_summary'] = media_summary
        state.setdefault('execution_logs', []).append("Media aggregation completed")
        
        logger.info(f"Media aggregation completed: {len(media_summary)} media types processed")
        
    except Exception as e:
        logger.error(f"Error in media aggregation: {e}")
        state.setdefault('execution_logs', []).append(f"Media aggregation error: {str(e)}")
    
    return state
