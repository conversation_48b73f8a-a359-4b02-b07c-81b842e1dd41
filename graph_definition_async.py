"""
Async Prayer Application Graph Definition
Defines the enhanced LangGraph workflow with async video generation and parallel media processing.
"""

from langgraph.graph import StateGraph, END
from models.prayer_state import PrayerState
from nodes.research_nodes import perform_research_node
from nodes.prayer_generation import (
    prepare_next_movement_node,
    generate_single_prayer_node,
    save_individual_prayer_node,
    should_continue_generating
)
from nodes.prayer_aggregation import (
    aggregate_prayers_node,
    generate_unified_prayer_node,
    save_unified_prayer_node,
)
from nodes.tts_nodes import (
    ask_audio_conversion_node,
    prepare_tts_text_node,
    generate_audio_node,
    audio_requested
)
from nodes.async_video_nodes import (
    parallel_media_generation_node,
    should_run_parallel_media_generation,
    media_aggregation_node
)
from nodes.scene_generation_nodes import generate_sacred_scenes_node # Import from new file

# Import the image prayer nodes with fallback
try:
    from nodes.updated_image_prayer_node import (
        generate_image_prayer_node,
        should_generate_image_prayer
    )
    print("[INFO] Using updated image prayer nodes with async support.")
except ImportError:
    try:
        from nodes.image_prayer_node import (
            generate_image_prayer_node,
            should_generate_image_prayer
        )
        print("[INFO] Using robust image prayer nodes.")
    except ImportError:
        from nodes.gemini_image_prayer import (
            generate_image_prayer_node,
            should_generate_image_prayer
        )
        print("[WARNING] Using original image prayer nodes.")

def create_async_prayer_workflow() -> StateGraph:
    """
    Create the enhanced LangGraph workflow with async video generation and parallel media processing.

    Returns:
        Compiled LangGraph workflow with async capabilities
    """
    # Initialize the graph with our enhanced state model
    workflow = StateGraph(PrayerState)

    # Add research node
    workflow.add_node("perform_research", perform_research_node)

    # Add individual prayer generation nodes
    workflow.add_node("prepare_movement", prepare_next_movement_node)
    workflow.add_node("generate_prayer", generate_single_prayer_node)
    workflow.add_node("save_prayer", save_individual_prayer_node)

    # Add unified prayer generation nodes
    workflow.add_node("aggregate_prayers", aggregate_prayers_node)
    workflow.add_node("generate_unified", generate_unified_prayer_node)
    workflow.add_node("save_unified", save_unified_prayer_node)

    # Add scene generation node (now imported)
    workflow.add_node("generate_scenes", generate_sacred_scenes_node)

    # Add parallel media generation node (NEW - handles video, image, audio async)
    workflow.add_node("parallel_media_generation", parallel_media_generation_node)

    # Add media aggregation node (NEW - combines all media outputs)
    workflow.add_node("media_aggregation", media_aggregation_node)

    # Add fallback nodes for individual media types (if parallel fails)
    workflow.add_node("fallback_tts", ask_audio_conversion_node)
    workflow.add_node("fallback_audio", generate_audio_node)
    workflow.add_node("fallback_image", generate_image_prayer_node)

    # Set the entry point - start with research
    workflow.set_entry_point("perform_research")

    # Connect research to prayer generation
    workflow.add_edge("perform_research", "prepare_movement")

    # Define the individual prayer generation cycle
    workflow.add_edge("prepare_movement", "generate_prayer")
    workflow.add_edge("generate_prayer", "save_prayer")

    # Conditional edge after saving individual prayer
    workflow.add_conditional_edges(
        "save_prayer",
        should_continue_generating,
        {
            "continue_generation": "prepare_movement",  # Loop back if more movements
            "aggregate": "aggregate_prayers",           # Go to aggregate when done looping
            # Removed "end_error" to allow flow to continue even if a prayer fails
        }
    )

    # Unified prayer generation flow
    workflow.add_edge("aggregate_prayers", "generate_unified")
    workflow.add_edge("generate_unified", "save_unified")

    # Scene generation for media
    workflow.add_edge("save_unified", "generate_scenes")

    # Parallel media generation decision point
    workflow.add_conditional_edges(
        "generate_scenes",
        should_run_parallel_media_generation,
        {
            "run_parallel_media": "parallel_media_generation",  # Run async media generation
            "skip_media": "media_aggregation"                   # Skip to aggregation
        }
    )

    # Media aggregation after parallel processing
    workflow.add_edge("parallel_media_generation", "media_aggregation")

    # Fallback paths for individual media types (if needed)
    # This needs to be more granular. We should check for each media type if it was requested
    # and if it failed in parallel_media_generation, then trigger its specific fallback.
    # For simplicity, let's assume if parallel_media_generation ran, we check its outputs.
    # If it didn't run (skip_media), then we still need to check if individual media was requested.

    # New conditional logic after media_aggregation
    workflow.add_conditional_edges(
        "media_aggregation",
        should_run_fallback_processing, # This function needs to be updated to be more granular
        {
            "run_tts_fallback": "fallback_tts",
            "run_image_fallback": "fallback_image",
            "complete": END
        }
    )

    # Fallback TTS flow
    workflow.add_conditional_edges(
        "fallback_tts",
        audio_requested, # This condition checks if audio was requested and not generated
        {
            "yes": "fallback_audio",
            "no": "fallback_image" # If TTS not needed, check for image fallback
        }
    )
    workflow.add_edge("fallback_audio", "fallback_image") # After audio, check for image fallback

    # Fallback image generation
    workflow.add_conditional_edges(
        "fallback_image",
        should_generate_image_prayer, # This condition checks if image was requested and not generated
        {
            "generate_image": END,  # Complete after fallback image
            "skip_image": END       # Complete without fallback image
        }
    )

    print("[INFO] Using async LangGraph workflow with parallel media processing")
    
    # Compile the graph
    return workflow.compile()

# Removed generate_sacred_scenes_node as it's now in nodes/scene_generation_nodes.py

def should_run_fallback_processing(state: PrayerState) -> str:
    """
    Determine if fallback processing is needed for failed media generation.
    This function now checks for specific media types using dict access.
    """
    # Access state as dict for consistency
    if isinstance(state, dict):
        # Check if video generation was requested and failed
        video_requested = state.get('generate_video', False)
        video_generated = state.get('generated_video') is not None
        
        # Check if image generation was requested and failed
        image_requested = state.get('image_prayer_requested', False)
        image_generated = state.get('image_prayer_generated', False) or (
            state.get('generated_images') is not None and len(state.get('generated_images', [])) > 0
        )
        
        # Check if audio generation was requested and failed
        audio_requested_flag = state.get('audio_conversion_requested', False)
        audio_generated = (state.get('audio_filepath') is not None or 
                          state.get('generated_audio') is not None)
    else:
        # Fallback to getattr for TypedDict objects
        video_requested = getattr(state, 'generate_video', False)
        video_generated = getattr(state, 'generated_video', None) is not None
        
        image_requested = getattr(state, 'image_prayer_requested', False)
        image_generated = (getattr(state, 'image_prayer_generated', False) or 
                          (getattr(state, 'generated_images', []) is not None and 
                           len(getattr(state, 'generated_images', [])) > 0))
        
        audio_requested_flag = getattr(state, 'audio_conversion_requested', False)
        audio_generated = (getattr(state, 'audio_filepath', None) is not None or 
                          getattr(state, 'generated_audio', None) is not None)

    # Prioritize audio fallback, then image
    # No video fallback node exists, so we skip video fallback
    if audio_requested_flag and not audio_generated:
        return "run_tts_fallback"
    
    if image_requested and not image_generated:
        return "run_image_fallback"
    
    return "complete"

def create_hybrid_prayer_workflow() -> StateGraph:
    """
    Create a hybrid workflow that can fallback to the original sequential processing
    if async processing fails.
    
    Returns:
        Compiled hybrid LangGraph workflow
    """
    # This would be a more conservative version that tries async first,
    # but falls back to the original workflow if needed
    workflow = StateGraph(PrayerState)
    
    # Add all nodes from both workflows
    # ... (implementation would include both async and original nodes)
    
    # Add decision points to choose between async and sequential
    # ... (based on system capabilities and user preferences)
    
    return workflow.compile()

# Utility function to choose the appropriate workflow
def create_prayer_workflow(enable_async: bool = True) -> StateGraph:
    """
    Factory function to create the appropriate prayer workflow.
    
    Args:
        enable_async: Whether to use the async workflow with video generation
        
    Returns:
        Compiled LangGraph workflow
    """
    if enable_async:
        try:
            return create_async_prayer_workflow()
        except ImportError as e:
            print(f"[WARNING] Async workflow unavailable: {e}")
            print("[INFO] Falling back to original workflow")
            from graph_definition_new import create_prayer_workflow as create_original
            return create_original()
    else:
        from graph_definition_new import create_prayer_workflow as create_original
        return create_original()

if __name__ == "__main__":
    # Test the workflow creation
    try:
        workflow = create_async_prayer_workflow()
        print("✅ Async prayer workflow created successfully")
    except Exception as e:
        print(f"❌ Error creating async workflow: {e}")
