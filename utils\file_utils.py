"""
File Utilities
Common file handling operations for the prayer application.
"""

import os
import datetime
import re

def sanitize_filename(filename):
    """
    Removes invalid characters and replaces spaces for filenames.

    Args:
        filename: Original filename to sanitize

    Returns:
        Sanitized filename
    """
    # Remove invalid characters (adjust based on OS if needed)
    sanitized = re.sub(r'[\\/*?:"<>|]', "", filename)
    # Replace spaces with underscores
    sanitized = sanitized.replace(" ", "_")
    # Limit length (optional)
    return sanitized[:100]  # Limit to 100 chars

def create_run_directory():
    """
    Creates a new run-specific output directory.

    Returns:
        Path to the created directory
    """
    base_output_dir = "Saved_Prayers"
    run_timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    run_output_dir = os.path.join(base_output_dir, f"Run_{run_timestamp}")

    try:
        os.makedirs(run_output_dir, exist_ok=True)
        print(f"[INFO] Created run directory: {run_output_dir}")
    except Exception as e:
        print(f"[ERROR] Failed to create run directory {run_output_dir}: {e}")
        raise

    return run_output_dir

def save_individual_prayer(filepath, prayer_text, prayer_focus_theme, religion):
    """
    Save an individual prayer to a file.

    Args:
        filepath: Path where to save the prayer
        prayer_text: The prayer content
        prayer_focus_theme: Theme of the prayer
        religion: Religious tradition of the prayer

    Returns:
        True if successful, False otherwise
    """
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(f"# Prayer for: {prayer_focus_theme}\n")
            f.write(f"# Spiritual Movement: {religion}\n")
            f.write(f"# Generated at: {datetime.datetime.now().isoformat()}\n\n")
            f.write(prayer_text)
        print(f"   [INFO] Prayer saved to {filepath}")
        return True
    except Exception as e:
        print(f"   [ERROR] Failed to save prayer to {filepath}: {e}")
        return False

def save_unified_prayer(filepath, prayer_text, prayer_focus_theme, model_name):
    """
    Save a unified prayer to a file.

    Args:
        filepath: Path where to save the prayer
        prayer_text: The prayer content
        prayer_focus_theme: Theme of the prayer
        model_name: Name of the model that generated the prayer

    Returns:
        True if successful, False otherwise
    """
    try:
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(f"# Unified Prayer\n")
            f.write(f"# Focus Theme: {prayer_focus_theme}\n")
            f.write(f"# Generated by: {model_name}\n")
            f.write(f"# Generated at: {datetime.datetime.now().isoformat()}\n\n")
            f.write(prayer_text if prayer_text else "# Generation failed or produced empty content.")
        print(f"   [INFO] Unified prayer saved to {filepath}")
        return True
    except Exception as e:
        print(f"   [ERROR] Failed to save unified prayer to {filepath}: {e}")
        return False

def save_verbal_prayer(filepath, verbal_text, source_filepath, model_name):
    """
    Save a verbal prayer with TTS annotations to a file.

    Args:
        filepath: Path where to save the verbal prayer
        verbal_text: The verbal prayer content with TTS annotations
        source_filepath: Path to the source prayer file
        model_name: Name of the model that generated the verbal prayer

    Returns:
        True if successful, False otherwise
    """
    try:
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(f"# Verbal Prayer (TTS-Optimized)\n")
            f.write(f"# Generated from: {source_filepath}\n")
            f.write(f"# Model used: {model_name}\n")
            f.write(f"# Generated at: {datetime.datetime.now().isoformat()}\n\n")
            f.write(verbal_text)
        print(f"   [INFO] Verbal prayer saved to {filepath}")
        return True
    except Exception as e:
        print(f"   [ERROR] Failed to save verbal prayer to {filepath}: {e}")
        return False

def read_prayer_file(filepath, skip_headers=True):
    """
    Read a prayer file, optionally skipping header lines and cleaning up the content.

    Args:
        filepath: Path to the prayer file
        skip_headers: Whether to skip lines starting with "#"

    Returns:
        Prayer text content, cleaned and optimized
    """
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            if skip_headers:
                lines = f.readlines()
                # Skip header lines and filter out empty lines
                prayer_content = "".join(line for line in lines if not line.strip().startswith("#") and line.strip())

                # Clean up the content
                cleaned_content = prayer_content.strip()

                # Remove excessive newlines (more than 2 consecutive)
                cleaned_content = re.sub(r'\n{3,}', '\n\n', cleaned_content)

                # Remove any trailing whitespace or newlines
                cleaned_content = cleaned_content.rstrip()

                return cleaned_content
            else:
                return f.read().strip()
    except Exception as e:
        print(f"   [ERROR] Failed to read prayer file {filepath}: {e}")
        return ""
