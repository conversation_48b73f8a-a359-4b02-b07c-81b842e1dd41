import logging
from typing import Dict, List, Optional
from models.prayer_state import PrayerState

logger = logging.getLogger(__name__)

def generate_sacred_scenes_node(state: PrayerState) -> PrayerState:
    """
    Generate sacred scenes from the unified prayer for video/image generation.
    This node is responsible for creating a list of scenes based on the prayer content,
    which can then be used by media generation services.

    Args:
        state: Current workflow state, expected to contain 'unified_prayer_markdown'.

    Returns:
        Updated state with 'sacred_scenes' populated.
    """
    logger.info("Node: Generating sacred scenes...")

    try:
        # Extract scenes from the unified prayer or create them
        # Access state as a dictionary
        sacred_scenes = state.get('sacred_scenes')
        
        if not sacred_scenes:
            scenes = []
            
            # Use the unified prayer text to create scenes
            prayer_text = state.get('unified_prayer_markdown', '')
            
            if prayer_text:
                # Simple scene extraction (could be enhanced with AI)
                # For now, providing a placeholder structure.
                # In a real scenario, an LLM would parse prayer_text to create these.
                scenes = [
                    {
                        "title": "Opening Invocation",
                        "content": "Sacred gathering in peaceful contemplation",
                        "visual_description": "Serene spiritual space with soft golden light, peaceful atmosphere, diverse spiritual symbols harmoniously arranged"
                    },
                    {
                        "title": "Unity Prayer",
                        "content": "Universal connection and understanding",
                        "visual_description": "Interconnected light patterns flowing between different cultural and spiritual symbols, representing unity in diversity"
                    },
                    {
                        "title": "Closing Blessing",
                        "content": "Gratitude and continued wisdom",
                        "visual_description": "Gentle luminous energy radiating outward, representing blessings and positive intentions spreading to the world"
                    }
                ]
            
            state['sacred_scenes'] = scenes
            execution_logs = state.get('execution_logs', [])
            execution_logs.append(f"Generated {len(scenes)} sacred scenes for media creation")
            state['execution_logs'] = execution_logs # Update the state with modified logs
            logger.info(f"Generated {len(scenes)} sacred scenes.")
        else:
            logger.info("Sacred scenes already exist in state, skipping generation.")
        
        return state
        
    except Exception as e:
        err_msg = f"Error generating sacred scenes: {str(e)}"
        logger.error(err_msg)
        execution_logs = state.get('execution_logs', [])
        execution_logs.append(err_msg)
        state['execution_logs'] = execution_logs # Update the state with modified logs
        state['error_message'] = err_msg # Propagate error
        return state
